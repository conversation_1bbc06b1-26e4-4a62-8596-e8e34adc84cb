#!/usr/bin/env node

/**
 * Environment Variables Checker for Vercel Deployment
 * Run this script to verify all required environment variables are set
 */

const requiredVars = [
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'OPENAI_API_KEY'
];

const optionalVars = [
  'NEXTAUTH_URL',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'GITHUB_ID',
  'GITHUB_SECRET',
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY'
];

console.log('🔍 Checking Environment Variables for Vercel Deployment\n');

let hasErrors = false;

// Check required variables
console.log('📋 Required Variables:');
requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: Set (${value.substring(0, 20)}...)`);
  } else {
    console.log(`❌ ${varName}: Missing`);
    hasErrors = true;
  }
});

console.log('\n📋 Optional Variables:');
optionalVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: Set (${value.substring(0, 20)}...)`);
  } else {
    console.log(`⚠️  ${varName}: Not set (optional)`);
  }
});

console.log('\n🔧 Recommendations:');

if (!process.env.NEXTAUTH_URL) {
  console.log('- Set NEXTAUTH_URL to your production domain for deployment');
}

if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
  console.log('- Consider setting up Google OAuth for better user experience');
}

if (!process.env.GITHUB_ID || !process.env.GITHUB_SECRET) {
  console.log('- Consider setting up GitHub OAuth for developer-friendly auth');
}

if (hasErrors) {
  console.log('\n❌ Some required environment variables are missing!');
  console.log('Please check your .env file or Vercel environment variables.');
  process.exit(1);
} else {
  console.log('\n✅ All required environment variables are set!');
  console.log('Your application should deploy successfully to Vercel.');
}
