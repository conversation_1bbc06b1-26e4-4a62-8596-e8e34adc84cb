{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/components/dashboard/chat-interface.tsx"], "sourcesContent": ["'use client'\n import ReactMarkdown from 'react-markdown';\nimport { useState, useRef, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card } from '@/components/ui/card'\nimport { Send, Bot, User } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport ReactMarkdown from 'react-markdown'\n\ninterface Message {\n  id: string\n  content: string\n  role: 'user' | 'assistant'\n  timestamp: Date\n}\n\nexport function ChatInterface() {\n  const [messages, setMessages] = useState<Message[]>([])\n  const [input, setInput] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [chatId, setChatId] = useState<string | null>(null)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const sendMessage = async () => {\n    if (!input.trim() || isLoading) return\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: input,\n      role: 'user',\n      timestamp: new Date(),\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInput('')\n    setIsLoading(true)\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: input,\n          chatId,\n        }),\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to send message')\n      }\n\n      const data = await response.json()\n      \n      const assistantMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        content: data.response,\n        role: 'assistant',\n        timestamp: new Date(),\n      }\n\n      setMessages(prev => [...prev, assistantMessage])\n      setChatId(data.chatId)\n\n    } catch (error) {\n      console.error('Error sending message:', error)\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        content: 'Sorry, there was an error processing your message. Please try again.',\n        role: 'assistant',\n        timestamp: new Date(),\n      }\n      setMessages(prev => [...prev, errorMessage])\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col h-full max-w-4xl mx-auto p-6\">\n      <div className=\"flex-1 overflow-auto space-y-4 mb-4\">\n        {messages.length === 0 ? (\n          <div className=\"text-center text-gray-400 mt-8\">\n            <Bot className=\"mx-auto h-12 w-12 mb-4\" />\n            <h3 className=\"text-lg font-medium text-white\">Start a conversation</h3>\n            <p>Ask me anything! I can help you with questions and access your saved notes.</p>\n          </div>\n        ) : (\n          messages.map((message) => (\n            <div\n              key={message.id}\n              className={cn(\n                'flex gap-3',\n                message.role === 'user' ? 'justify-end' : 'justify-start'\n              )}\n            >\n              {message.role === 'assistant' && (\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                    <Bot className=\"w-4 h-4 text-white\" />\n                  </div>\n                </div>\n              )}\n              \n              <Card className={cn(\n                'max-w-[80%] p-3',\n                message.role === 'user'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-800 text-gray-100'\n              )}>\n                <p className=\"whitespace-pre-wrap\"><ReactMarkdown>{message.content}</ReactMarkdown></p>\n              </Card>\n\n              {message.role === 'user' && (\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center\">\n                    <User className=\"w-4 h-4 text-gray-300\" />\n                  </div>\n                </div>\n              )}\n            </div>\n          ))\n        )}\n        \n        {isLoading && (\n          <div className=\"flex gap-3 justify-start\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                <Bot className=\"w-4 h-4 text-white\" />\n              </div>\n            </div>\n            <Card className=\"max-w-[80%] p-3 bg-gray-800 text-gray-100\">\n              <div className=\"flex space-x-1\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n              </div>\n            </Card>\n          </div>\n        )}\n        \n        <div ref={messagesEndRef} />\n      </div>\n\n      <div className=\"flex gap-2\">\n        <Input\n          value={input}\n          onChange={(e) => setInput(e.target.value)}\n          onKeyPress={handleKeyPress}\n          placeholder=\"Type your message...\"\n          disabled={isLoading}\n          className=\"flex-1\"\n        />\n        <Button onClick={sendMessage} disabled={isLoading || !input.trim()}>\n          <Send className=\"w-4 h-4\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;;AAiBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,MAAM;YACN,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,mBAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS,KAAK,QAAQ;gBACtB,MAAM;gBACN,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;YAC/C,UAAU,KAAK,MAAM;QAEvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,MAAM;gBACN,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;0CAAE;;;;;;;;;;;+BAGL,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;4BAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;;gCAG3C,QAAQ,IAAI,KAAK,6BAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,mBACA,QAAQ,IAAI,KAAK,SACb,2BACA;8CAEJ,cAAA,8OAAC;wCAAE,WAAU;kDAAsB,cAAA,8OAAC,wLAAA,CAAA,UAAa;sDAAE,QAAQ,OAAO;;;;;;;;;;;;;;;;gCAGnE,QAAQ,IAAI,KAAK,wBAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BA1BjB,QAAQ,EAAE;;;;;oBAkCpB,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGnB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;;kCAMzG,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBACJ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,YAAY;wBACZ,aAAY;wBACZ,UAAU;wBACV,WAAU;;;;;;kCAEZ,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,UAAU,aAAa,CAAC,MAAM,IAAI;kCAC9D,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}]}